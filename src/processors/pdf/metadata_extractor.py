"""
PDF元数据提取器

负责从PDF文件中提取元数据信息，支持多种提取引擎。
"""

from datetime import datetime
from typing import Optional
from loguru import logger

from .models import PDFMetadata
from .utils import calculate_file_hash

# PDF处理库导入和可用性检查
try:
    from docling.document_converter import DocumentConverter
    DOCLING_AVAILABLE = True
    logger.debug("docling库已加载，支持元数据提取")
except ImportError:
    DOCLING_AVAILABLE = False
    logger.debug("docling库未安装，元数据提取功能受限")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
    logger.debug("PyPDF2库已加载，支持元数据提取")
except ImportError:
    PYPDF2_AVAILABLE = False
    logger.debug("PyPDF2库未安装，元数据提取功能受限")


class PDFMetadataExtractor:
    """PDF元数据提取器"""
    
    def __init__(self):
        self.extraction_methods = []
        if PYPDF2_AVAILABLE:
            self.extraction_methods.append(self._extract_with_pypdf2)
        if DOCLING_AVAILABLE:
            self.extraction_methods.append(self._extract_with_docling)
    
    def extract_metadata(self, filepath: str, file_content: bytes) -> PDFMetadata:
        """
        提取PDF元数据
        
        Args:
            filepath: PDF文件路径
            file_content: PDF文件内容
            
        Returns:
            PDFMetadata: 提取的元数据
        """
        metadata = PDFMetadata(
            file_size=len(file_content),
            file_hash=calculate_file_hash(file_content)
        )
        
        # 尝试不同的提取方法
        for method in self.extraction_methods:
            try:
                method(filepath, metadata)
                break
            except Exception as e:
                logger.warning(f"元数据提取方法失败: {e}")
                continue
        
        return metadata
    
    def _extract_with_pypdf2(self, filepath: str, metadata: PDFMetadata) -> None:
        """
        使用PyPDF2提取元数据
        
        Args:
            filepath: PDF文件路径
            metadata: 元数据对象（会被修改）
        """
        if not PYPDF2_AVAILABLE:
            return
            
        try:
            with open(filepath, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                # 基本信息
                metadata.page_count = len(reader.pages)
                metadata.is_encrypted = reader.is_encrypted
                
                # 文档信息
                if reader.metadata:
                    info = reader.metadata
                    metadata.title = info.get('/Title')
                    metadata.author = info.get('/Author')
                    metadata.subject = info.get('/Subject')
                    metadata.creator = info.get('/Creator')
                    metadata.producer = info.get('/Producer')
                    
                    # 日期处理
                    creation_date = info.get('/CreationDate')
                    if creation_date:
                        metadata.creation_date = self._parse_pdf_date(creation_date)
                    
                    mod_date = info.get('/ModDate')
                    if mod_date:
                        metadata.modification_date = self._parse_pdf_date(mod_date)
                
                logger.debug(f"PyPDF2元数据提取成功: {metadata.page_count}页")
                
        except Exception as e:
            logger.warning(f"PyPDF2元数据提取失败: {e}")
            raise
    
    def _extract_with_docling(self, filepath: str, metadata: PDFMetadata) -> None:
        """
        使用docling提取元数据
        
        Args:
            filepath: PDF文件路径
            metadata: 元数据对象（会被修改）
        """
        if not DOCLING_AVAILABLE:
            return
            
        try:
            converter = DocumentConverter()
            result = converter.convert(filepath)
            
            # 从docling结果中提取元数据
            if hasattr(result, 'document') and hasattr(result.document, 'meta'):
                meta = result.document.meta
                if hasattr(meta, 'page_count'):
                    metadata.page_count = meta.page_count
                    
            logger.debug(f"docling元数据提取成功")
            
        except Exception as e:
            logger.warning(f"docling元数据提取失败: {e}")
            raise
    
    def _parse_pdf_date(self, date_str: str) -> Optional[datetime]:
        """
        解析PDF日期格式
        
        Args:
            date_str: PDF日期字符串
            
        Returns:
            Optional[datetime]: 解析后的日期时间对象
        """
        try:
            # PDF日期格式: D:YYYYMMDDHHmmSSOHH'mm'
            if date_str.startswith('D:'):
                date_str = date_str[2:]
            
            # 提取年月日时分秒
            if len(date_str) >= 14:
                year = int(date_str[0:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                hour = int(date_str[8:10])
                minute = int(date_str[10:12])
                second = int(date_str[12:14])
                
                return datetime(year, month, day, hour, minute, second)
                
        except (ValueError, IndexError) as e:
            logger.debug(f"PDF日期解析失败: {date_str}, 错误: {e}")
        
        return None
